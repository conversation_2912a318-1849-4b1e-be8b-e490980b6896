<template>
  <div class="ai-chat-view">
    <!-- 侧边栏 -->
    <div class="sidebar-container" :class="{ collapsed: sidebarCollapsed }">
      <ChatSidebar v-show="!sidebarCollapsed" @toggle-sidebar="toggleSidebar" />

      <!-- 折叠后的迷你侧边栏 -->
      <div v-show="sidebarCollapsed" class="mini-sidebar">
        <el-tooltip content="展开侧边栏" placement="right">
          <el-button
            :icon="Menu"
            @click="toggleSidebar"
            text
            size="large"
            class="expand-btn"
          />
        </el-tooltip>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="chat-main">
      <!-- 顶部标题栏 -->
      <div class="chat-header">
        <div class="header-left">
          <el-button
            v-if="sidebarCollapsed"
            :icon="Menu"
            @click="toggleSidebar"
            text
            size="large"
            class="sidebar-toggle-btn"
          />
          
          <!-- 功能模式切换标签 -->
          <el-tabs 
            v-model="activeMode" 
            class="mode-tabs"
            @tab-change="handleModeChange"
          >
            <el-tab-pane label="AI对话" name="chat">
              <template #label>
                <div class="tab-label">
                  <el-icon><ChatLineRound /></el-icon>
                  <span>AI对话</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="视频生成" name="video">
              <template #label>
                <div class="tab-label">
                  <el-icon><VideoPlay /></el-icon>
                  <span>视频生成</span>
                  <el-badge 
                    v-if="aiChatStore.hasActiveVideoTasks" 
                    :value="aiChatStore.pollingTaskIds.size" 
                    type="primary" 
                    class="task-badge"
                  />
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div class="header-right">
          <!-- AI盘儿快捷开关 - 仅在Electron环境显示 -->
          <el-tooltip 
            v-if="isElectron" 
            :content="aiPanEnabled ? 'AI盘儿已启用' : '启用AI盘儿悬浮窗'" 
            placement="bottom"
          >
            <el-button
              :icon="Camera"
              @click="handleToggleAiPan"
              :type="aiPanEnabled ? 'primary' : 'default'"
              :loading="aiPanLoading"
              size="default"
              class="ai-pan-toggle-btn"
            >
              AI盘儿
            </el-button>
          </el-tooltip>

          <el-button
            :icon="Plus"
            @click="handleCreateNewSession"
            type="primary"
            size="default"
          >
            新对话
          </el-button>

          <el-button
            :icon="Setting"
            @click="goToSettings"
            size="default"
          >
            设置
          </el-button>

          <el-button
            :icon="Delete"
            @click="handleClearChat"
            :disabled="aiChatStore.messages.length === 0"
            size="default"
          >
            清空对话
          </el-button>
        </div>
      </div>

      <!-- 角色设置栏 - 仅在AI对话模式下显示 -->
      <RoleSelector 
        v-show="activeMode === 'chat'"
        ref="roleSelectorRef"
        :web-search-mode="webSearchMode"
        :document-mode="documentMode"
        :image-understanding-mode="imageUnderstandingMode"
        @web-search-toggle="handleWebSearchToggle"
        @document-mode-toggle="handleDocumentModeToggle"
        @image-understanding-toggle="handleImageUnderstandingToggle"
      />

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- AI对话内容 -->
        <div v-show="activeMode === 'chat'" class="chat-content">
        <!-- 消息列表 -->
        <div class="chat-messages" ref="messagesContainer">
          <!-- 欢迎消息 -->
          <div v-if="aiChatStore.messages.length === 0" class="welcome-message">
            <div class="welcome-content">
              <el-icon class="welcome-icon"><ChatLineRound /></el-icon>
              <h3>{{ aiChatStore.hasApiKey ? '欢迎使用 AI 助手' : '配置 API Key 开始使用' }}</h3>
              <p v-if="!aiChatStore.hasApiKey" class="welcome-tip">
                请先在设置页面配置 API Key 才能开始与 AI 助手对话
              </p>
              <p v-else class="welcome-tip">
                点击左侧"新对话"按钮开始聊天，或直接在下方输入您的问题
              </p>
            </div>
          </div>

          <!-- 简化版虚拟滚动：只显示最近的消息 -->
          <div v-if="aiChatStore.messages.length > 0" class="virtual-message-list" ref="virtualListRef" @scroll="handleScroll">
            <ChatMessage
              v-for="message in visibleMessages"
              :key="message.id"
              :message="message"
              :streaming="message.id === aiChatStore.streamingMessageId"
              @retry="handleRetryMessage"
            />
            
            <!-- 智能搜索进度组件 -->
            <div v-if="isSearching && searchProgress" class="search-progress-container">
              <div class="progress-card">
                <div class="progress-content">
                  <el-icon class="progress-icon"><Loading /></el-icon>
                  <div class="progress-text">
                    <span class="progress-message">
                      {{ searchProgress.message }}
                    </span>
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: `${(getCurrentStep() + 1) * 20}%` }"></div>
                    </div>
                  </div>
                </div>
                
                <div v-if="searchProgress.keywords?.length" class="keywords-display">
                  <span class="keywords-label">提取关键词:</span>
                  <el-tag
                    v-for="keyword in searchProgress.keywords.slice(0, 3)"
                    :key="keyword"
                    size="small"
                    class="keyword-tag"
                  >
                    {{ keyword }}
                  </el-tag>
                  <span v-if="searchProgress.keywords.length > 3" class="more-keywords">
                    +{{ searchProgress.keywords.length - 3 }}个
                  </span>
                </div>
              </div>
            </div>

            <!-- 加载指示器 -->
            <div v-if="aiChatStore.isLoading && !aiChatStore.isStreaming" class="loading-indicator">
              <div class="loading-content">
                <el-icon class="loading-icon"><Loading /></el-icon>
                <span>AI 正在思考中...</span>
              </div>
            </div>
          </div>

          <!-- 滚动到底部按钮 -->
          <div 
            v-show="showScrollToBottomBtn" 
            class="scroll-to-bottom-btn"
            @click="scrollToBottom"
          >
            <el-button 
              :icon="ArrowDown" 
              circle 
              size="default"
              type="primary"
            />
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
          <ChatInputBox
            ref="chatInputBoxRef"
            v-model="inputMessage"
            :disabled="!aiChatStore.hasApiKey || aiChatStore.isLoading"
            :placeholder="aiChatStore.hasApiKey ? '输入您的问题...' : '请先配置 API Key'"
            :web-search-mode="webSearchMode"
            :document-mode="documentMode"
            :image-understanding-mode="imageUnderstandingMode"
            :image-generation-mode="imageGenerationMode"
            @send="handleSendMessage"
            @sendImage="handleSendImageGeneration"
            @sendImageUnderstanding="handleSendImageUnderstanding"
            @sendFileAnalysis="handleSendFileAnalysis"
            @sendWebSearch="handleWebSearchMessage"
            @stop="handleStopMessage"
            @image-generation-toggle="handleImageGenerationToggle"
          />
        </div>
        </div>

        <!-- 视频生成内容 -->
        <div v-show="activeMode === 'video'" class="video-content">
          <VideoGenerationPanel />
        </div>
      </div>
    </div>

    <!-- iframe 预览弹窗 -->
    <el-dialog
      v-model="previewDialogVisible"
      :title="previewTitle"
      width="80%"
      top="5vh"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      destroy-on-close
    >
      <div class="iframe-container">
        <iframe
          v-if="previewUrl"
          :src="previewUrl"
          frameborder="0"
          width="100%"
          height="600px"
          sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
        ></iframe>
        <div v-else class="loading-placeholder">
          <el-icon class="loading-icon"><Loading /></el-icon>
          <span>加载中...</span>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="copyPreviewUrl" :icon="DocumentCopy">复制链接</el-button>
          <el-button @click="openInNewTab" type="primary">在新标签页打开</el-button>
          <el-button @click="previewDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ChatLineRound, Loading, Setting, Delete, Plus, Menu, Camera, ArrowDown, DocumentCopy, VideoPlay } from '@element-plus/icons-vue'
import { useAIChatStore } from '@/stores/aiChatStore'
import { useThemeStore } from '@/stores/theme'
import ChatMessage from './components/ChatMessage.vue'
import ChatInputBox from './components/ChatInputBox.vue'
import ChatSidebar from '@/components/ai-chat/ChatSidebar.vue'
import RoleSelector from './components/RoleSelector.vue'
import VideoGenerationPanel from '@/components/VideoGenerationPanel.vue'
import type { ImageGenerationConfig } from '@/types/aiChat'
import { webSearch } from '@/api/aiChat'
// 移除有问题的虚拟滚动库

const router = useRouter()
const aiChatStore = useAIChatStore()
const themeStore = useThemeStore()

// 响应式数据
const inputMessage = ref('')
const messagesContainer = ref<HTMLElement>()
const virtualListRef = ref()
const sidebarCollapsed = ref(true)
const chatInputBoxRef = ref() // 新增：ChatInputBox组件引用
const roleSelectorRef = ref() // 新增：RoleSelector组件引用
const webSearchMode = ref(false) // 网络搜索模式状态
const documentMode = ref(false) // 文档模式状态  
const imageUnderstandingMode = ref(false) // 图片理解模式状态
const imageGenerationMode = ref(false) // 图片生成模式状态
const showScrollToBottomBtn = ref(false) // 滚动到底部按钮显示状态

// 智能搜索进度状态
interface SearchProgress {
  stage: 'analyzing' | 'keywords' | 'searching' | 'analyzing_results' | 'generating' | 'completed' | 'error' | 'fallback'
  message: string
  keywords?: string[]
  timestamp: number
  data?: any
}

const searchProgress = ref<SearchProgress>({
  stage: 'analyzing',
  message: '',
  keywords: [],
  timestamp: 0
})

const isSearching = ref(false)

// 功能模式相关状态
const activeMode = ref<'chat' | 'video'>('chat') // 当前激活的功能模式

// iframe 预览弹窗相关状态
const previewDialogVisible = ref(false)
const previewUrl = ref('')
const previewTitle = ref('')

// AI盘儿相关状态
const aiPanLoading = ref(false)
const aiPanEnabled = computed(() => themeStore.aiPanEnabled)

// 检查是否在Electron环境
const isElectron = computed(() => {
  return typeof window !== 'undefined' && (window as any).electronAPI !== undefined
})

// 简化版虚拟滚动：限制显示的消息数量
const MAX_VISIBLE_MESSAGES = 100 // 最多显示100条消息

const visibleMessages = computed(() => {
  const messages = aiChatStore.messages

  // 如果消息数量超过限制，只显示最新的消息
  if (messages.length > MAX_VISIBLE_MESSAGES) {
    return messages.slice(-MAX_VISIBLE_MESSAGES)
  }

  return messages
})

// 方法
// 计算当前搜索进度步骤
const getCurrentStep = () => {
  if (!searchProgress.value) return 0
  
  switch (searchProgress.value.stage) {
    case 'analyzing':
      return 0  // 20%
    case 'extracting_keywords':
      return 1  // 40%
    case 'keywords':
    case 'fallback':
      return 2  // 60%
    case 'searching':
      return 3  // 80%
    case 'analyzing_results':
      return 4  // 100%
    case 'generating':
      return 4  // 保持100%，因为这时进度条会被隐藏
    case 'completed':
      return 4
    case 'error':
      return 0
    default:
      return 0
  }
}

// 网络搜索消息处理
const handleWebSearchMessage = async (query: string) => {
  try {
    inputMessage.value = ''
    
    // 添加用户搜索消息
    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      content: query,
      timestamp: new Date(),
      isWebSearch: true
    }
    aiChatStore.messages.push(userMessage)

    // 保存用户搜索消息到会话（如果有当前会话）
    if (aiChatStore.currentSession) {
      try {
        await aiChatStore.saveMessageToCurrentSession('user', query)
      } catch (error) {
        console.error('Save user web search message error:', error)
      }
    }

    // 初始化搜索进度状态
    isSearching.value = true
    searchProgress.value = {
      stage: 'analyzing',
      message: 'AI正在分析您的问题...',
      timestamp: Date.now()
    }

    // 设置加载状态
    aiChatStore.status = 'loading'

    const isStreaming = aiChatStore.isStreamingEnabled

    if (isStreaming) {
      // 流式模式处理 - 增强版
      let aiContent = ''
      let aiMessageId: string | null = null
      let aiMessageCreated = false
      
      // 预先创建进度显示消息
      const progressMessage = {
        id: `progress-${Date.now()}`,
        role: 'assistant' as const,
        content: '',
        timestamp: new Date(),
        isSearchProgress: true
      }
      aiChatStore.messages.push(progressMessage)
      
      // 设置流式状态（但不设置streamingMessageId，等内容开始时再设置）
      aiChatStore.status = 'streaming'

      const searchResponse = await webSearch(query, true, aiChatStore.deepThinking, (data: string) => {
        try {
          // 处理流式数据
          if (data === '[DONE]') {
            // 流式结束标记，隐藏进度显示
            isSearching.value = false
            // 移除进度消息
            const progressIndex = aiChatStore.messages.findIndex(m => m.isSearchProgress)
            if (progressIndex !== -1) {
              aiChatStore.messages.splice(progressIndex, 1)
            }
            return
          }
          
          // 尝试解析JSON格式的SSE进度事件
          if (data.startsWith('{') && data.endsWith('}')) {
            try {
              const eventData = JSON.parse(data)
              
              // 处理进度事件
              if (eventData.stage && eventData.message) {
                searchProgress.value = {
                  stage: eventData.stage,
                  message: eventData.message,
                  keywords: eventData.data?.keywords || [],
                  timestamp: eventData.timestamp || Date.now(),
                  data: eventData.data
                }
                
                // 如果是降级模式，立即隐藏进度显示
                if (eventData.stage === 'fallback') {
                  setTimeout(() => {
                    isSearching.value = false
                    // 移除进度消息
                    const progressIndex = aiChatStore.messages.findIndex(m => m.isSearchProgress)
                    if (progressIndex !== -1) {
                      aiChatStore.messages.splice(progressIndex, 1)
                    }
                  }, 1000) // 1秒后隐藏，让用户看到降级提示
                }
                
                // 更新进度显示消息
                const progressIndex = aiChatStore.messages.findIndex(m => m.isSearchProgress)
                if (progressIndex !== -1) {
                  // 进度消息会在模板中自动更新
                }
                return
              }
              
              // 处理AI推理和内容数据
              if (eventData.type === 'content' && eventData.content) {
                // 开始输出内容时，创建AI消息并隐藏进度显示
                if (!aiMessageCreated) {
                  aiMessageId = `ai-${Date.now()}`
                  const aiMessage = {
                    id: aiMessageId,
                    role: 'assistant' as const,
                    content: '',
                    timestamp: new Date(),
                    isWebSearchResult: true
                  }
                  aiChatStore.messages.push(aiMessage)
                  aiChatStore.streamingMessageId = aiMessageId
                  aiMessageCreated = true
                  
                  // 隐藏进度显示
                  isSearching.value = false
                  const progressIndex = aiChatStore.messages.findIndex(m => m.isSearchProgress)
                  if (progressIndex !== -1) {
                    aiChatStore.messages.splice(progressIndex, 1)
                  }
                }
                
                // 更新AI消息内容
                aiContent += eventData.content
                if (aiMessageId) {
                  const messageIndex = aiChatStore.messages.findIndex(m => m.id === aiMessageId)
                  if (messageIndex !== -1) {
                    aiChatStore.messages[messageIndex].content = aiContent
                  }
                }
              }
              // 忽略reasoning类型的推理数据
              return
            } catch (parseError) {
              // JSON解析失败，可能是错误信息
              if (data.includes('error')) {
                console.error('Stream error:', data)
                isSearching.value = false
                searchProgress.value = {
                  stage: 'error',
                  message: '搜索失败',
                  timestamp: Date.now()
                }
                return
              }
            }
          }
          
          // 非JSON格式的纯文本数据，直接追加
          if (data && !data.includes('"type"') && !data.includes('"stage"')) {
            // 开始输出内容时，创建AI消息并隐藏进度显示
            if (!aiMessageCreated) {
              aiMessageId = `ai-${Date.now()}`
              const aiMessage = {
                id: aiMessageId,
                role: 'assistant' as const,
                content: '',
                timestamp: new Date(),
                isWebSearchResult: true
              }
              aiChatStore.messages.push(aiMessage)
              aiChatStore.streamingMessageId = aiMessageId
              aiMessageCreated = true
              
              // 隐藏进度显示
              isSearching.value = false
              const progressIndex = aiChatStore.messages.findIndex(m => m.isSearchProgress)
              if (progressIndex !== -1) {
                aiChatStore.messages.splice(progressIndex, 1)
              }
            }
            
            // 更新AI消息内容
            aiContent += data
            if (aiMessageId) {
              const messageIndex = aiChatStore.messages.findIndex(m => m.id === aiMessageId)
              if (messageIndex !== -1) {
                aiChatStore.messages[messageIndex].content = aiContent
              }
            }
          }
        } catch (error) {
          console.error('Error processing stream data:', error)
        }
      })

      // 流式传输完成后的状态重置
      aiChatStore.status = 'idle'
      
      ElMessage.success('网络搜索完成')
      
    } else {
      // 非流式模式处理 - 保持原有逻辑，但添加进度显示
      searchProgress.value = {
        stage: 'searching',
        message: '正在搜索网络资源...',
        timestamp: Date.now()
      }
      
      const searchResponse = await webSearch(query, false, aiChatStore.deepThinking)
      
      searchProgress.value = {
        stage: 'generating',
        message: '正在生成答案...',
        timestamp: Date.now()
      }
      
      if (searchResponse && searchResponse.data) {
        const aiMessage = {
          id: `ai-search-${Date.now()}`,
          role: 'assistant' as const,
          content: searchResponse.data.aiAnalysis || '网络搜索暂时无法获取结果，请稍后再试。',
          timestamp: new Date(),
          isWebSearchResult: true
        }
        aiChatStore.messages.push(aiMessage)
        
        // 处理搜索链接（如果有的话）
        if (searchResponse.data.searchLinks) {
          let fullContent = aiMessage.content
          const searchLinks = searchResponse.data.searchLinks
          if (searchLinks && searchLinks.length > 0) {
            fullContent += '\n\n**参考链接：**\n'
            searchLinks.forEach((link: any, index: number) => {
              fullContent += `${index + 1}. [${link.title}](${link.link}) - ${link.media}\n`
            })
          }
          
          searchProgress.value = {
            stage: 'completed',
            message: '搜索完成',
            timestamp: Date.now()
          }
          
          aiMessage.content = fullContent
        }
        
        // 保存AI回复到会话
        if (aiChatStore.currentSession) {
          try {
            await aiChatStore.saveMessageToCurrentSession('assistant', aiMessage.content)
          } catch (error) {
            console.error('Save AI web search response error:', error)
          }
        }
        
        aiChatStore.status = 'idle'
      }
      
      // 隐藏进度显示
      isSearching.value = false
    }
    
    scrollToBottom()
  } catch (error) {
    console.error('Web search error:', error)
    
    // 更新进度状态为错误
    searchProgress.value = {
      stage: 'error',
      message: '搜索失败',
      timestamp: Date.now()
    }
    
    aiChatStore.status = 'idle'
    isSearching.value = false
    
    ElMessage.error('网络搜索失败')
  }
}

const handleSendMessage = async () => {
  const message = inputMessage.value.trim()
  if (!message || !aiChatStore.hasApiKey || aiChatStore.isLoading || aiChatStore.isStreaming) return

  // 如果没有当前会话，先创建一个（用户发送消息时才创建）
  if (!aiChatStore.currentSession) {
    try {
      await aiChatStore.createNewSession()
    } catch (error) {
      console.error('Create session error:', error)
      ElMessage.error('创建对话失败，请检查后端服务是否启动')
      return
    }
  }

  try {
    // 检查是否是网络搜索模式
    if (webSearchMode.value) {
      // 网络搜索模式：调用专用的搜索处理逻辑
      await handleWebSearchMessage(message)
    } else {
      // 普通聊天模式
      await aiChatStore.sendMessage(message)
    }
    scrollToBottom()
  } catch (error) {
    console.error('Send message error:', error)
  }
}

const handleStopMessage = () => {
  aiChatStore.stopStreaming()
}

const handleSendImageGeneration = async (prompt: string, config: ImageGenerationConfig) => {
  if (!prompt.trim() || !aiChatStore.hasApiKey || aiChatStore.isLoading || aiChatStore.isStreaming) return

  // 如果没有当前会话，先创建一个
  if (!aiChatStore.currentSession) {
    try {
      await aiChatStore.createNewSession()
    } catch (error) {
      console.error('Create session error:', error)
      ElMessage.error('创建对话失败，请检查后端服务是否启动')
      return
    }
  }

  try {
    // 构建图片生成请求
    const imageRequest = {
      message: prompt,
      isImageGeneration: true,
      imageSize: config.imageSize,
      batchSize: config.batchSize,
      guidanceScale: config.guidanceScale,
      numInferenceSteps: config.numInferenceSteps,
      image: config.uploadedImage
    }

    // 调用图片生成API（复用现有的sendMessage方法）
    await aiChatStore.sendMessage(prompt, imageRequest)
    scrollToBottom()
  } catch (error) {
    console.error('Image generation error:', error)
    ElMessage.error('图片生成失败')
  }
}

const handleSendImageUnderstanding = async (message: string, imageBase64: string) => {
  if (!message.trim() || !imageBase64 || !aiChatStore.hasApiKey || aiChatStore.isLoading || aiChatStore.isStreaming) return

  // 如果没有当前会话，先创建一个
  if (!aiChatStore.currentSession) {
    try {
      await aiChatStore.createNewSession()
    } catch (error) {
      console.error('Failed to create session:', error)
      ElMessage.error('创建会话失败')
      return
    }
  }

  try {
    // 发送图片理解请求
    await aiChatStore.sendMessage(message, {
      message: message,
      isImageUnderstanding: true,
      uploadedImage: imageBase64
    })
    // 发送成功后，自动清除图片理解状态，避免用户需要手动清除
    if (chatInputBoxRef.value && typeof chatInputBoxRef.value.clearUploadedImageForUnderstanding === 'function') {
      chatInputBoxRef.value.clearUploadedImageForUnderstanding()
    }

    // 滚动到底部
    scrollToBottom()
  } catch (error) {
    console.error('Image understanding error:', error)
    ElMessage.error('图片理解失败')
  }
}

const handleSendFileAnalysis = async (message: string, fileIds: string[]) => {
  if (!message.trim() || !fileIds.length || !aiChatStore.hasApiKey || aiChatStore.isLoading || aiChatStore.isStreaming) return

  // 如果没有当前会话，先创建一个
  if (!aiChatStore.currentSession) {
    try {
      await aiChatStore.createNewSession()
    } catch (error) {
      console.error('Failed to create session:', error)
      ElMessage.error('创建会话失败')
      return
    }
  }

  try {
    // 发送文件分析请求
    await aiChatStore.sendMessage(message, {
      message: message,
      isFileAnalysis: true,
      uploadedFileIds: fileIds
    })

    // 自动清除文件分析状态，避免用户需要手动清除
    // 暂时保留文件，因为可能要继续问关于这些文件的问题
    // 如果需要自动清除，可以取消注释下面的代码：
    // if (chatInputBoxRef.value && typeof chatInputBoxRef.value.clearUploadedFiles === 'function') {
    //   chatInputBoxRef.value.clearUploadedFiles()
    // }

    // 滚动到底部
    scrollToBottom()
  } catch (error) {
    console.error('File analysis error:', error)
    ElMessage.error('文件分析失败')
  }
}
const handleRetryMessage = async () => {
  try {
    await aiChatStore.retryLastMessage()
    scrollToBottom()
  } catch (error) {
    console.error('Retry message error:', error)
  }
}

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 处理功能模式切换
const handleModeChange = (mode: 'chat' | 'video') => {
  activeMode.value = mode
  
  // 切换到视频模式时，启用视频模式并加载历史记录
  if (mode === 'video') {
    aiChatStore.toggleVideoMode(true)
  } else if (mode === 'chat') {
    aiChatStore.toggleVideoMode(false)
  }
  
  // 记住用户选择
  localStorage.setItem('ai_active_mode', mode)
}

const handleCreateNewSession = async () => {
  try {
    await aiChatStore.createNewSession()
  } catch (error) {
    console.error('Create session error:', error)
    ElMessage.error('创建新对话失败')
  }
}

const handleClearChat = async () => {
  try {
    await aiChatStore.clearChat()
  } catch (error) {
    console.error('Clear chat error:', error)
  }
}

const goToSettings = () => {
  router.push('/dashboard/settings')
}

// 统一的AI模式管理函数
const setAIMode = (mode: 'document' | 'image_understanding' | 'image_generation' | 'none') => {
  // 首先关闭所有模式
  documentMode.value = false
  imageUnderstandingMode.value = false
  imageGenerationMode.value = false
  
  // 然后开启指定模式
  switch (mode) {
    case 'document':
      documentMode.value = true
      break
    case 'image_understanding':
      imageUnderstandingMode.value = true
      break
    case 'image_generation':
      imageGenerationMode.value = true
      break
    case 'none':
      // 所有模式都已经关闭
      break
  }
  
  // 同步到ChatInputBox组件
  if (chatInputBoxRef.value) {
    // 通知InputBox更新其内部的aiMode状态
    if (mode === 'document' && typeof chatInputBoxRef.value.setMode === 'function') {
      chatInputBoxRef.value.setMode('file_analysis')
    } else if (mode === 'image_understanding' && typeof chatInputBoxRef.value.setMode === 'function') {
      chatInputBoxRef.value.setMode('image_understanding')
    } else if (mode === 'image_generation' && typeof chatInputBoxRef.value.setMode === 'function') {
      chatInputBoxRef.value.setMode('image_generation')
    } else if (mode === 'none' && typeof chatInputBoxRef.value.setMode === 'function') {
      chatInputBoxRef.value.setMode('text')
    }
  }
}

// 网络搜索模式切换处理
const handleWebSearchToggle = (enabled: boolean) => {
  webSearchMode.value = enabled
  
  // 如果开启网络搜索，需要关闭图片生成模式
  if (enabled && imageGenerationMode.value) {
    setAIMode('none')
  }
  
  console.log('网络搜索模式:', enabled ? '已启用' : '已禁用')
}

// 文档模式切换处理
const handleDocumentModeToggle = (enabled: boolean) => {
  if (enabled) {
    setAIMode('document')
  } else {
    setAIMode('none')
  }
  console.log('文档模式:', enabled ? '已启用' : '已禁用')
}

// 图片理解模式切换处理
const handleImageUnderstandingToggle = (enabled: boolean) => {
  if (enabled) {
    setAIMode('image_understanding')
  } else {
    setAIMode('none')
  }
  console.log('图片理解模式:', enabled ? '已启用' : '已禁用')
}

// 图片生成模式切换处理
const handleImageGenerationToggle = (enabled: boolean) => {
  if (enabled) {
    // 如果开启图片生成模式，需要关闭网络搜索
    if (webSearchMode.value) {
      webSearchMode.value = false
      // RoleSelector会通过props监听到webSearchMode的变化并自动更新
    }
    setAIMode('image_generation')
  } else {
    setAIMode('none')
  }
  console.log('图片生成模式:', enabled ? '已启用' : '已禁用')
}

// 处理链接预览
const handleLinkPreview = (url: string, title?: string) => {
  previewUrl.value = url
  previewTitle.value = title || '链接预览'
  previewDialogVisible.value = true
}

// 在新标签页打开链接
const openInNewTab = () => {
  if (previewUrl.value) {
    window.open(previewUrl.value, '_blank', 'noopener,noreferrer')
  }
}

// 复制预览链接
const copyPreviewUrl = async () => {
  if (!previewUrl.value) {
    ElMessage.warning('没有可复制的链接')
    return
  }

  try {
    // 使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(previewUrl.value)
      ElMessage.success('链接已复制到剪贴板')
    } else {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = previewUrl.value
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        document.execCommand('copy')
        ElMessage.success('链接已复制到剪贴板')
      } catch (err) {
        ElMessage.error('复制失败，请手动复制')
        console.error('复制失败:', err)
      } finally {
        document.body.removeChild(textArea)
      }
    }
  } catch (err) {
    ElMessage.error('复制失败，请手动复制')
    console.error('复制失败:', err)
  }
}

// AI盘儿开关处理函数
const handleToggleAiPan = async () => {
  if (!isElectron.value) {
    ElMessage.info('AI盘儿功能仅在桌面版中可用')
    return
  }

  const newEnabled = !aiPanEnabled.value
  aiPanLoading.value = true

  try {
    if (newEnabled) {
      // 启用时显示AI盘儿窗口
      const result = await (window as any).electronAPI.aiPan.showWindow()
      if (!result.success) {
        ElMessage.error(`启用失败: ${result.error}`)
        // 如果启用失败，不更新状态
        return
      }
    } else {
      // 禁用时隐藏AI盘儿窗口
      await (window as any).electronAPI.aiPan.hideWindow()
    }
    
    // 更新状态
    themeStore.setAiPanEnabled(newEnabled)
  } catch (error) {
    console.error('AI盘儿窗口控制失败:', error)
    ElMessage.error('窗口控制失败，请重试')
  } finally {
    aiPanLoading.value = false
  }
}

const scrollToBottom = () => {
  nextTick(() => {
    if (virtualListRef.value) {
      // 滚动虚拟列表容器到底部
      virtualListRef.value.scrollTop = virtualListRef.value.scrollHeight
    } else if (messagesContainer.value) {
      // 兜底方案：直接滚动容器
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
    // 滚动到底部后隐藏按钮
    showScrollToBottomBtn.value = false
  })
}

// 处理滚动事件，控制滚动到底部按钮的显示
const handleScroll = () => {
  // 检查虚拟列表容器的滚动状态
  if (!virtualListRef.value) return
  
  const { scrollTop, scrollHeight, clientHeight } = virtualListRef.value
  // 当距离底部超过100px时显示按钮
  const isNearBottom = scrollHeight - scrollTop - clientHeight < 100
  showScrollToBottomBtn.value = !isNearBottom && aiChatStore.messages.length > 0
}

// 监听消息变化，自动滚动到底部
watch(() => aiChatStore.messages.length, () => {
  scrollToBottom()
}, { flush: 'post' })

// 监听流式消息更新
watch(() => aiChatStore.streamingMessageId, () => {
  scrollToBottom()
}, { flush: 'post' })

// 生命周期
onMounted(() => {
  // 初始化时滚动到底部
  scrollToBottom()

  // 恢复用户上次选择的模式
  const savedMode = localStorage.getItem('ai_active_mode') as 'chat' | 'video'
  if (savedMode && ['chat', 'video'].includes(savedMode)) {
    activeMode.value = savedMode
    if (savedMode === 'video') {
      aiChatStore.toggleVideoMode(true)
    }
  }

  // 添加全局点击事件监听，处理预览链接
  document.addEventListener('click', handleGlobalClick)

  // 监听AI盘儿窗口隐藏事件（仅在Electron环境）
  if (isElectron.value && (window as any).electronAPI?.onAiPanWindowHidden) {
    ;(window as any).electronAPI.onAiPanWindowHidden(() => {
      // console.log('AI盘儿窗口已隐藏，更新状态')
      themeStore.setAiPanEnabled(false)
    })
  }

  // 新用户没有会话记录是正常的，不需要自动创建
})

// 清理事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
})

// 全局点击事件处理
const handleGlobalClick = (event: Event) => {
  const target = event.target as HTMLElement
  
  // 检查是否点击的是预览链接
  if (target.tagName === 'A' && target.classList.contains('preview-link')) {
    event.preventDefault() // 阻止默认跳转行为
    
    const url = target.getAttribute('data-url')
    const title = target.getAttribute('data-title')
    
    if (url) {
      handleLinkPreview(url, title || '链接预览')
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-chat-view {
  height: 100%;
  display: flex;
  background: var(--el-bg-color);
  overflow: hidden;
}

.sidebar-container {
  width: 230px;
  transition: width 0.3s ease;
  flex-shrink: 0;

  &.collapsed {
    width: 45px;
  }
}

.mini-sidebar {
  width: 45px;
  height: 100%;
  background: var(--el-bg-color-page);
  border-right: 1px solid var(--el-border-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;

  .expand-btn {
    width: 44px;
    height: 44px;
    border-radius: 8px;
  }
}

  // 全局文本换行规则
  * {
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

.chat-header {
  padding: 6px 24px;
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--el-bg-color);
  box-shadow: 0 1px 4px var(--el-box-shadow-light);

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .sidebar-toggle-btn {
      width: 40px;
      height: 40px;
      border-radius: 6px;
    }
    
    .mode-tabs {
      margin-left: 8px;
      
      :deep(.el-tabs__header) {
        margin: 0;
        
        .el-tabs__nav-wrap {
          &::after {
            display: none; // 隐藏下划线
          }
        }
      }
      
      :deep(.el-tabs__item) {
        padding: 8px 16px;
        font-size: 14px;
        
        .tab-label {
          display: flex;
          align-items: center;
          gap: 6px;
          
          .task-badge {
            margin-left: 4px;
            
            :deep(.el-badge__content) {
              font-size: 10px;
              min-width: 16px;
              height: 16px;
              line-height: 16px;
            }
          }
        }
      }
      
      :deep(.el-tabs__active-bar) {
        background: var(--el-color-primary);
      }
    }

    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .header-right {
    display: flex;
    gap: 8px;
    align-items: center;

    // AI盘儿按钮特殊样式
    .ai-pan-toggle-btn {
      position: relative;
      transition: all 0.3s ease;

      // 启用状态下的特殊效果
      &.el-button--primary {
        box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
        
        &:hover {
          box-shadow: 0 0 12px rgba(64, 158, 255, 0.5);
        }
      }

      // 禁用状态下的悬浮效果
      &.el-button--default {
        &:hover {
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }
      }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow: hidden;
  padding: 0px 15px;
  display: flex;
  flex-direction: column;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  position: relative; // 添加相对定位以支持绝对定位的按钮

  .welcome-message {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;

    .welcome-content {
      text-align: center;
      max-width: 400px;

      .welcome-icon {
        font-size: 48px;
        color: var(--el-color-primary);
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 12px 0;
        font-size: 20px;
        color: var(--el-text-color-primary);
      }

      .welcome-tip {
        margin: 0;
        color: var(--el-text-color-regular);
        line-height: 1.6;
      }
    }
  }

  .loading-indicator {
    display: flex;
    justify-content: center;
    padding: 16px;

    .loading-content {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--el-text-color-regular);
      font-size: 14px;

      .el-icon {
        font-size: 16px;
      }
    }
  }

  // 滚动到底部按钮
  .scroll-to-bottom-btn {
    position: absolute;
    bottom: 50px;
    right: 30px;
    z-index: 1000;
    
    .el-button {
      width: 40px;
      height: 40px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border: none;
      
      &:hover {
        box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
        transition: all 0.2s ease;
      }
    }
  }
}

// 简化版虚拟滚动样式
.virtual-message-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.video-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: var(--el-fill-color-lighter);
}

.chat-input {
  padding: 11px 24px;
  background: var(--el-bg-color);
}

.api-key-prompt {
  margin: 16px 24px;
}

// 响应式设计
@media (max-width: 768px) {
  .chat-header {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .header-right {
      align-self: stretch;
      justify-content: flex-end;
    }
  }

  .chat-messages {
    padding: 16px;
  }

  .chat-input {
    padding: 12px 16px;
  }

  .api-key-prompt {
    margin: 12px 16px;
  }
}

// iframe 预览弹窗样式
.iframe-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--el-border-color);
  
  iframe {
    display: block;
    border-radius: 8px;
  }
  
  .loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 600px;
    color: var(--el-text-color-regular);
    font-size: 14px;
    gap: 8px;
    
    .loading-icon {
      font-size: 16px;
      animation: rotate 2s linear infinite;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  
  .el-button {
    &:first-child {
      // 复制按钮样式
      border-color: var(--el-color-success);
      color: var(--el-color-success);
      
      &:hover {
        background-color: var(--el-color-success);
        color: white;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 预览链接样式
:deep(.preview-link) {
  color: var(--el-color-primary) !important;
  text-decoration: none !important;
  border-bottom: 1px dashed var(--el-color-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    color: var(--el-color-primary-light-3) !important;
    border-bottom-color: var(--el-color-primary-light-3);
    background-color: var(--el-color-primary-light-9);
    padding: 2px 4px;
    border-radius: 4px;
  }
  
  &:before {
    content: "🔗 ";
    font-size: 12px;
    opacity: 0.7;
  }
}

// 搜索结果项样式
:deep(.search-result-item) {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--el-color-primary-light-7);
  }
  
  .result-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    
    .source-icon {
      width: 16px;
      height: 16px;
      border-radius: 2px;
      flex-shrink: 0;
    }
    
    .source-name {
      font-size: 12px;
      color: var(--el-text-color-regular);
      background: var(--el-color-info-light-9);
      padding: 2px 6px;
      border-radius: 4px;
      font-weight: 500;
    }
  }
  
  .result-title {
    font-weight: 600;
    color: var(--el-text-color-primary);
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 6px;
  }
  
  .result-link {
    font-size: 13px;
    
    .preview-link {
      word-break: break-all;
      line-height: 1.3;
    }
  }
}

/* 智能搜索进度组件样式 - 简洁版 */
.search-progress-container {
  margin: 12px 0;
  animation: slideInUp 0.3s ease-out;
  
  .progress-card {
    background: var(--el-bg-color-page);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .progress-content {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;
      
      .progress-icon {
        animation: rotate 1.5s linear infinite;
        color: var(--el-color-primary);
        font-size: 16px;
        flex-shrink: 0;
      }
      
      .progress-text {
        flex: 1;
        
        .progress-message {
          display: block;
          font-size: 13px;
          color: var(--el-text-color-regular);
          margin-bottom: 8px;
          line-height: 1.4;
        }
        
        .progress-bar {
          height: 3px;
          background: var(--el-border-color-light);
          border-radius: 2px;
          overflow: hidden;
          
          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-3));
            border-radius: 2px;
            transition: width 0.3s ease;
          }
        }
      }
    }
    
    .keywords-display {
      margin-top: 8px;
      
      .keywords-label {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-right: 8px;
      }
      
      .keyword-tag {
        margin: 2px 4px 2px 0;
        font-size: 11px;
        padding: 2px 8px;
      }
      
      .more-keywords {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        margin-left: 4px;
      }
    }
  }
}

/* 参考来源新样式 */
:deep(.search-references) {
  margin-top: 16px;
  
  .reference-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }
  
  .reference-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 8px;
    
    .reference-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 8px 12px;
      background: var(--el-bg-color-page);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 6px;
      transition: all 0.2s ease;
      
      &:hover {
        border-color: var(--el-color-primary-light-5);
        background: var(--el-color-primary-light-9);
      }
      
      .ref-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background: var(--el-color-primary);
        color: white;
        border-radius: 50%;
        font-size: 11px;
        font-weight: 600;
        flex-shrink: 0;
        margin-top: 2px;
      }
      
      .ref-content {
        flex: 1;
        min-width: 0;
        
        .ref-title {
          font-size: 13px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 2px;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .ref-url {
          font-size: 11px;
          color: var(--el-text-color-secondary);
          
          .preview-link {
            color: var(--el-color-primary);
            text-decoration: none;
            word-break: break-all;
            
            &:hover {
              text-decoration: underline;
              color: var(--el-color-primary-dark-2);
            }
          }
        }
      }
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
